import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { cloneDeep, flatten } from "lodash";
import { message } from "antd";

import { useEnum, useLazyKVMap, useLoading, useQueryAll } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { dataToHtml, findChildNode, transformData } from "../helpers";
import { query } from "redux-store/stores";
import { LOAI_DICH_VU, ROLES, ENUM } from "constants/index";
import { isArray } from "utils/index";
import { t } from "i18next";

export const DanhSachContext = React.createContext();

export const DanhSachProvider = ({ children }) => {
  const { listData, refresh, pageNumber, totalPages, loading } = useSelector(
    (state) => state.nbDichVuKyThuat
  );
  const { showLoading, hideLoading } = useLoading();

  const [dataTRANG_THAI_DICH_VU] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [dataLOAI_PTTT] = useEnum(ENUM.LOAI_PTTT);

  const [treeStatus, setTreeStatus] = useState({});
  const [rowDetailExpand, setRowDetailExpand] = useState({});
  const [page, setPage] = useState(0);
  const [selectedRowKeysByGroup, setSelectedRowKeysByGroup] = useState({});
  //ref
  const refCurrentPage = useRef(0);
  const refScroll = useRef(null);
  const refContainer = useRef(null);
  const refReTry = useRef(0);
  const refSaveData = useRef({
    children: [],
  });
  const refInnerScrollElement = useRef([]);

  const refScrollPosition = useRef({
    scrollLeft: 0,
  }).current;

  const mapRefScroll = useRef(new Map()).current;
  const refMap = useRef(new Map()).current;

  const listRef = useRef({
    refScroll,
    refContainer,
    refSaveData,
    refInnerScrollElement,
    refScrollPosition,
    mapRefScroll,
    refMap,
    outerScrollEl: null,
  });

  const {
    chiDinhKhamBenh: { xemKetQua, huyXemKetQua },
    nbDichVuKyThuat: { onChangeInputSearch },
    nbDichVuKyThuat: { getList, updateData },
  } = useDispatch();

  const { data: listAllSoHieuGiuong } = useQueryAll(
    query.soHieuGiuong.queryAllSoHieuGiuong
  );
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);

  const [getSoHieuGiuong] = useLazyKVMap(listAllSoHieuGiuong);
  const [getKhoa] = useLazyKVMap(listAllKhoa);

  const dataTransformed = useMemo(() => {
    const _dataTransform = transformData(
      listData.map((item) => {
        item.pageNumber = pageNumber;
        return item;
      })
    );
    const data = [...refMap.set(pageNumber, _dataTransform).values()]
      .map((i) => i?.children)
      .flat();

    refSaveData.current = {
      children: data,
    };
    return refSaveData.current;
  }, [listData, pageNumber]);

  const loaiPtttMap = useMemo(() => {
    return new Map(dataLOAI_PTTT.map((item) => [item.id, item.ten]));
  }, [dataLOAI_PTTT]);

  const refInnerScroll = useCallback((node, loaiDichVu) => {
    if (node !== null) {
      const { scrollWidth, clientWidth } = node;
      mapRefScroll.set(loaiDichVu, scrollWidth);

      if (scrollWidth > clientWidth) {
        refScroll.current.style.width = `${
          Math.max(...mapRefScroll.values()) + 42 //might change when styling
        }px`;
        refInnerScrollElement.current =
          document.querySelectorAll(".inner-scroll");
        scroll();
      }
    }
  }, []);

  const scroll = () => {
    refInnerScrollElement.current.forEach((scroll) => {
      scroll.scrollLeft = refScrollPosition.scrollLeft;
    });
    refContainer.current.classList.toggle(
      "ping-left",
      refScrollPosition.scrollLeft === 0
    );
    if (listRef.current.outerScrollEl) {
      refContainer.current.classList.toggle(
        "ping-right",
        listRef.current.outerScrollEl.clientWidth +
          refScrollPosition.scrollLeft ===
          refScroll.current.clientWidth
      );
    }
  };

  useEffect(() => {
    if (refresh) {
      setPage(0);
      refSaveData.current = {
        children: [],
      };
      refMap.clear();
    }
  }, [refresh]);

  useEffect(() => {
    const scroller = document.getElementById("outer-scroll");

    const handleWheel = (e) => {
      if (e.deltaX !== 0) {
        e.preventDefault();
      }

      if (e.shiftKey && e.deltaY !== 0) {
        e.preventDefault();
        scroller.scrollLeft += e.deltaY;
      }
      if (e.deltaX !== 0 && Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        scroller.scrollLeft += e.deltaX;
      }
    };
    listRef.current.outerScrollEl = scroller;
    const handleScroll = (e) => {
      refScrollPosition.scrollLeft = e.currentTarget.scrollLeft;
      scroll();
    };

    document.addEventListener("wheel", handleWheel, {
      passive: false,
    });
    scroller.addEventListener("scroll", handleScroll);
    return () => {
      document.removeEventListener("wheel", handleWheel);
      scroller.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const continueFetch = () => {
    if (pageNumber + 1 >= totalPages || loading) return;
    updateData({ refresh: false });
    getList({ page: page + 1 })
      .then(() => {
        setPage(page + 1);
        refCurrentPage.current = page + 1;
      })
      .finally(() => {
        if (refCurrentPage.current === page && refReTry.current < 4) {
          continueFetch();
          refReTry.current++;
        } else {
          refReTry.current = 0;
        }
      });
  };

  const _listData = useMemo(() => {
    const data = cloneDeep(dataTransformed);
    Object.values(treeStatus).forEach((tree) => {
      const findNode = findChildNode(data, tree);
      if (findNode && !tree.open) {
        findNode.children = [];
      }
    });
    const _data = [];
    data.children.forEach((child1) => {
      _data.push(dataToHtml(child1.properties, 0));
      child1.children.forEach((child2, index2) => {
        _data.push(dataToHtml(child2.properties, 1));
        child2.children.forEach((child3, index3) => {
          _data.push(dataToHtml(child3.properties, 2));
          child3.children.forEach((child4, index4) => {
            _data.push(dataToHtml(child4.properties, 3));
            _data.push(dataToHtml(child4.properties, 4));

            child4.children.forEach((child5, index5) => {
              child5.properties.index = index5 + 1;
              child5.properties.className = {
                "odd-row": index5 % 2 === 0,
                "even-row": index5 % 2 === 1,
              };
              if (child4.children.length - 1 === index5) {
                child5.properties.className = {
                  ...child5.properties.className,
                  "rounded-b-4": true,
                  "border-bottom-main": true,
                };
              }
              _data.push(dataToHtml(child5.properties, 5));
              const lastChildGroup =
                child1.children.length - 1 === index2 &&
                child2.children.length - 1 === index3 &&
                child3.children.length - 1 === index4 &&
                child4.children.length - 1 === index5;
              if (lastChildGroup) {
                child5.properties.className = {
                  lastGroupItem: true,
                };
              }
              if (child4.children.length - 1 === index5) {
                _data.push(dataToHtml(child5.properties, 6));
              }
            });
          });
        });
      });
    });
    return _data;
  }, [dataTransformed, treeStatus]);

  const listTrangThaiDichVuMemo = useMemo(() => {
    return dataTRANG_THAI_DICH_VU.reduce((acc, item) => {
      acc[item.id] = item.ten;
      return acc;
    }, {});
  }, [dataTRANG_THAI_DICH_VU]);

  const onSelectRow = useCallback(
    (groupId, id) => (e) => {
      const checked = e.target.checked;

      setSelectedRowKeysByGroup((prev) => {
        const prevGroupKeys = prev[groupId] || [];
        const selectedRowKeys = new Set(prevGroupKeys);

        if (checked) {
          selectedRowKeys.add(id);
        } else {
          selectedRowKeys.delete(id);
        }

        return {
          ...prev,
          [groupId]: [...selectedRowKeys],
        };
      });
    },
    []
  );

  const getGroupKey = useCallback((data) => {
    return `${data?.nbDotDieuTriId}-${data?.loaiDichVu}-${data?.nhomDichVuCap1Id}-${data?.nhomDichVuCap2Id}-${data?.soPhieu}`;
  }, []);

  const listAllDsDichVu = useMemo(() => {
    return flatten(_listData.map((item) => item.dsDichVu));
  }, [_listData]);

  // lấy groupData theo groupId từ listAllDsDichVu
  const getGroupData = useCallback(
    (groupId) => {
      return (listAllDsDichVu || []).filter(
        (item) => getGroupKey(item) === groupId
      );
    },
    [listAllDsDichVu, getGroupKey]
  );

  // select all group
  const onSelectAllGroup = useCallback(
    (groupId, checked) => {
      const groupData = getGroupData(groupId);
      const allIds = groupData.map((item) => item.id);

      setSelectedRowKeysByGroup((prev) => ({
        ...prev,
        [groupId]: checked ? allIds : [],
      }));
    },
    [getGroupData]
  );

  // check full group
  const isSelectedAllGroup = useCallback(
    (groupId) => {
      const groupData = getGroupData(groupId);
      const selectedKeys = selectedRowKeysByGroup[groupId] || [];
      return groupData.length > 0 && selectedKeys.length === groupData.length;
    },
    [getGroupData, selectedRowKeysByGroup]
  );

  // check indeterminate
  const isIndeterminateGroup = useCallback(
    (groupId) => {
      const groupData = getGroupData(groupId);
      const selectedKeys = selectedRowKeysByGroup[groupId] || [];
      return selectedKeys.length > 0 && selectedKeys.length < groupData.length;
    },
    [getGroupData, selectedRowKeysByGroup]
  );

  const showCheckBox = useCallback((record) => {
    if (record.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM) {
      return checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN]);
    }
    if (record.loaiDichVu == LOAI_DICH_VU.CDHA) {
      return checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA]);
    }
    if (record.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT) {
      return checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT]);
    }
    return false;
  }, []);

  const getGroupPrefixKey = useCallback((data) => {
    return `${data?.nbDotDieuTriId}-${data?.loaiDichVu}-${data?.nhomDichVuCap1Id}`;
  }, []);

  const onXemKetQua = useCallback(
    (record) => async (e) => {
      e.stopPropagation();
      const prefixKey = getGroupPrefixKey(record);

      const dsId = Object.entries(selectedRowKeysByGroup)
        .filter(([groupKey]) => groupKey.startsWith(prefixKey))
        .flatMap(([, ids]) => ids);

      if (!isArray(dsId, true)) {
        message.error(t("khamBenh.chiDinh.vuiLongChonDichVu"));
        return;
      }
      try {
        showLoading();
        let s = await xemKetQua({
          payload: dsId,
          loaiDichVu: record.loaiDichVu,
        });

        if (s.code === 0) {
          onChangeInputSearch({});
        }
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    },
    [selectedRowKeysByGroup, getGroupKey]
  );

  const onHuyXemKetQua = useCallback(
    (record) => async (e) => {
      e.stopPropagation();
      const prefixKey = getGroupPrefixKey(record);

      const dsId = Object.entries(selectedRowKeysByGroup)
        .filter(([groupKey]) => groupKey.startsWith(prefixKey))
        .flatMap(([, ids]) => ids);

      if (!isArray(dsId, true)) {
        message.error(t("khamBenh.chiDinh.vuiLongChonDichVu"));
        return;
      }
      try {
        showLoading();
        let s = await huyXemKetQua({
          payload: dsId,
          loaiDichVu: record.loaiDichVu,
        });

        if (s.code === 0) {
          onChangeInputSearch({});
        }
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    },
    []
  );

  return (
    <DanhSachContext.Provider
      value={{
        treeStatus,
        setTreeStatus,
        rowDetailExpand,
        setRowDetailExpand,
        listTrangThaiDichVuMemo,
        listData: _listData,
        continueFetch,
        dataTransformed,
        loaiPtttMap,
        refScroll,
        refContainer,
        refInnerScroll,
        refScrollPosition,
        listAllSoHieuGiuong,
        listAllKhoa,
        getSoHieuGiuong,
        getKhoa,
        onSelectRow,
        selectedRowKeysByGroup,
        getGroupKey,
        onSelectAllGroup,
        isSelectedAllGroup,
        isIndeterminateGroup,
        showCheckBox,
        onXemKetQua,
        onHuyXemKetQua,
      }}
    >
      {children}
    </DanhSachContext.Provider>
  );
};
