import React, { useContext } from "react";
import moment from "moment";
import { t } from "i18next";
import classNames from "classnames";
import { checkOpenFc } from "../../helpers";
import { DanhSachContext } from "../../context";
import { RowLevel1Styled } from "./styled";
import { Tooltip } from "components";
import { SVG } from "assets";

const RowLevel1 = ({ record }) => {
  const key = `${record.rootId}-${record.nhomDichVuCap1Id}`;
  const {
    treeStatus,
    setTreeStatus,
    showCheckBox,
    onXemKetQua,
    onHuyXemKetQua,
  } = useContext(DanhSachContext);

  return (
    <RowLevel1Styled className="group-header-level2 group-border-lr flex align-items-center">
      <button
        className={classNames("table-expand-icon", {
          "table-expand-icon-collapsed": checkOpenFc(treeStatus[key]?.open),
        })}
        onClick={(e) => {
          setTreeStatus((prev) => ({
            ...prev,
            [key]: {
              id: record.rootId,
              children: {
                id: record.nhomDichVuCap1Id,
              },
              open: checkOpenFc(prev[key]?.open),
            },
          }));
        }}
      />
      <div className="title uppercase">{record.tenNhomDichVuCap1}</div>
      <div className="title">
        [
        {record.thoiGianThucHien &&
          moment(record.thoiGianThucHien).format("DD/MM/YYYY")}{" "}
        - {record.tenKhoaChiDinh}]
      </div>
      {showCheckBox(record) && (
        <div className="flex gap-8">
          <Tooltip title={t("khamBenh.xacNhanXemKetQua")} placement="bottom">
            <SVG.IcTick
              style={{
                width: 20,
                height: 20,
              }}
              className="cursor-pointer"
              onClick={onXemKetQua(record)}
            />
          </Tooltip>
          <Tooltip title={t("common.huyXacNhanXemKetQua")} placement="bottom">
            <SVG.IcCloseCircle
              style={{
                width: 20,
                height: 20,
              }}
              className="cursor-pointer"
              color={"var(--color-red-primary)"}
              onClick={onHuyXemKetQua(record)}
            />
          </Tooltip>
        </div>
      )}
    </RowLevel1Styled>
  );
};

export default RowLevel1;
